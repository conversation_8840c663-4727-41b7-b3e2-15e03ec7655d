import os
import sqlite3

from common import CommonUtil
from common.bean.LogEntry import LogEntry
from common.bean.SongEntry import SongEntry
from common.bean.RecordAction import RecordAction


class DB:
    """
    数据库类，负责与SQLite数据库进行交互
    规范：
        1. private方法不调用commit方法，交由public方法来调用。
        2. 为了避免单个事务的作用范围混乱，保证一个public方法对应一个事务。
    """
    def __init__(self, db_path):
        self.db_path = db_path
        self.db_name = os.path.basename(db_path)
        self.conn = sqlite3.connect(self.db_path)
        self.cursor = self.conn.cursor()
        self._initialize_db()

    def _initialize_db(self):
        # 创建歌曲表songs
        # 以 (singer, song) 为主键，hash值为唯一索引
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS songs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            singer VA<PERSON><PERSON><PERSON>(255) NOT NULL,
            song VARCHAR(255) NOT NULL,
            album VARCHAR(255) DEFAULT '',
            hash_value TEXT NOT NULL UNIQUE,
            import_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(singer, song)
        )
        ''')

        # 检查是否需要添加album字段（为了兼容旧数据库）
        self.cursor.execute("PRAGMA table_info(songs)")
        columns = [column[1] for column in self.cursor.fetchall()]
        if 'album' not in columns:
            self.cursor.execute('ALTER TABLE songs ADD COLUMN album VARCHAR(255) DEFAULT ""')
            print("已为songs表添加album字段")

        # 按歌手查询，显式添加索引：
        self.cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_singer ON songs (singer)
        ''')

        # 创建日志表logs
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            version_id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            action VARCHAR(255) NOT NULL,
            song VARCHAR(255) NOT NULL
        )
        ''')
        self.conn.commit()

    def get_hashes_from_db(self) -> set[str]:
        """
        返回一个hash值的集合
        :return:
        """
        self.cursor.execute("SELECT hash_value FROM songs")
        rows = self.cursor.fetchall()
        return {row[0] for row in rows}

    def get_songs_from_db(self) -> set[str]:
        """
        获取数据库中的所有歌曲名, 格式: "歌手名 - 歌曲名.mp3"
        :return: 歌曲名集合
        """
        self.cursor.execute("SELECT singer, song FROM songs")
        rows = self.cursor.fetchall()
        return {CommonUtil.get_file_name(row[0], row[1]) for row in rows}

    def get_song_by_hash(self, file_hash) -> str:
        """
        根据hash值获取歌曲名
        :param file_hash: 歌曲hash值
        :return: 歌曲名, 格式: "歌手名 - 歌曲名.mp3"
        """
        self.cursor.execute('''
            SELECT singer, song FROM songs WHERE hash_value = ?
            ''', (file_hash,))
        row = self.cursor.fetchone()
        return CommonUtil.get_file_name(row[0], row[1]) if row else None

    def exist(self, singer, song) -> bool:
        """
        检查数据库中是否存在指定的歌曲
        :param singer: 歌手名
        :param song: 歌曲名
        :return: True if exists, False otherwise
        """
        self.cursor.execute('''
            SELECT * FROM songs WHERE singer = ? AND song = ?
            ''', (singer, song))
        return self.cursor.fetchone() is not None

    def _insert_song_entry(self, singer, song, file_hash, album=''):
        """
        插入歌曲信息到数据库
        :param singer: 歌手名
        :param song: 歌曲名
        :param file_hash: 歌曲hash值
        :param album: 专辑名
        """
        # 插入歌曲信息
        self.cursor.execute('''
            INSERT INTO songs (singer, song, album, hash_value)
            VALUES (?, ?, ?, ?)
            ''', (singer, song, album, file_hash))

    def _delete_song_entry(self, singer, song):
        """
        删除歌曲信息
        :param singer: 歌手名
        :param song: 歌曲名
        """
        self.cursor.execute('''
            DELETE FROM songs WHERE singer = ? AND song = ?
            ''', (singer, song))

    def get_version(self) -> int:
        self.cursor.execute("SELECT MAX(version_id) FROM logs")
        result = self.cursor.fetchone()[0]
        return result if result is not None else 0

    def _record_version(self, action: RecordAction, song: str):
        """
        记录变更信息，递增版本号
        :param action: 操作类型
        :param song: 变更的歌曲
        """
        self.cursor.execute('''
        INSERT INTO logs (action, song)
        VALUES (?, ?)
        ''', (action.name, song))

    def register_song(self, file_path) -> bool:
        """
        注册歌曲到数据库
        :param file_path: 歌曲文件路径
        :return: 是否注册成功
        """
        # 提取歌手名和歌曲名
        file_name = CommonUtil.format_song_file_name(os.path.basename(file_path))
        singer, song = CommonUtil.parse_singer_and_song(file_name)

        # 提取专辑信息
        album = self._extract_album_info(file_path)

        # 插入数据库
        try:
            self._insert_song_entry(singer, song, CommonUtil.compute_file_hash(file_path), album)
            self._record_version(RecordAction.ADD_SONG, file_name)
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Failed to register song {singer} - {song}: {e}")
            self.conn.rollback()
            return False

    def _extract_album_info(self, file_path):
        """
        提取歌曲的专辑信息
        :param file_path: 歌曲文件路径
        :return: 专辑名称
        """
        try:
            from mutagen.mp3 import MP3
            from mutagen.id3 import ID3

            if not os.path.exists(file_path):
                return ""

            audio = MP3(file_path, ID3=ID3)
            if not audio.tags:
                return ""

            # 查找专辑信息
            album = ""
            if 'TALB' in audio.tags:  # ID3v2.4 专辑标签
                album_tag = audio.tags['TALB']
                if hasattr(album_tag, 'text') and album_tag.text:
                    album = str(album_tag.text[0])
                else:
                    album = str(album_tag)
            elif 'TAL' in audio.tags:   # ID3v2.2 专辑标签
                album_tag = audio.tags['TAL']
                if hasattr(album_tag, 'text') and album_tag.text:
                    album = str(album_tag.text[0])
                else:
                    album = str(album_tag)

            # 清理专辑名称，移除可能的控制字符
            if album:
                # 移除不可打印字符
                album = ''.join(char for char in album if char.isprintable())
                album = album.strip()

                # 如果专辑名称看起来像二进制数据（全是十六进制字符），则返回空
                if len(album) > 10 and all(c in '0123456789abcdefABCDEF' for c in album):
                    print(f"检测到可能的二进制数据作为专辑名，忽略: {album[:50]}...")
                    return ""

            return album
        except Exception as e:
            print(f"提取专辑信息时出错 {file_path}: {e}")
            return ""

    def unregister_song(self, file_path) -> bool:
        """
        从数据库中注销歌曲
        :param file_path: 歌曲文件路径 或者 歌曲名
        """
        # 提取歌手名和歌曲名
        file_name = os.path.basename(file_path)
        singer, song = CommonUtil.parse_singer_and_song(file_name)

        # 删除数据库中的记录
        try:
            self._delete_song_entry(singer, song)
            self._record_version(RecordAction.DELETE_SONG, file_name)
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error deleting song {singer} - {song}: {e}")
            return False

    def close(self):
        """
        关闭数据库连接
        """
        self.conn.close()

    def db_exists(self, folder) -> bool:
        """
        检查文件夹下面是否存在数据库文件music_manager.db
        :param folder: 文件夹路径
        :return: True if exists, False otherwise
        """
        db_path = os.path.join(folder, self.db_name)
        return os.path.exists(db_path)

    def get_logs(self, start_version) -> list[LogEntry]:
        """
        获取指定版本号之后的所有日志
        :param start_version: 起始版本号
        :return: 日志列表，LogEntry列表
        """
        self.cursor.execute('''
        SELECT * FROM logs WHERE version_id > ?
        ''', (start_version,))
        rows = self.cursor.fetchall()
        logs = []
        for row in rows:
            logs.append(LogEntry(
                version_id=row[0],
                timestamp=row[1],
                action=RecordAction[row[2]],
                song=row[3]
            ))
        return logs

    def get_all_logs(self, limit=None, offset=0) -> list[LogEntry]:
        """
        获取所有日志，支持分页
        :param limit: 限制返回的条目数，None表示不限制
        :param offset: 偏移量
        :return: 日志列表，LogEntry列表
        """
        if limit is None:
            self.cursor.execute('''
            SELECT * FROM logs ORDER BY version_id DESC
            ''')
        else:
            self.cursor.execute('''
            SELECT * FROM logs ORDER BY version_id DESC LIMIT ? OFFSET ?
            ''', (limit, offset))
        rows = self.cursor.fetchall()
        logs = []
        for row in rows:
            logs.append(LogEntry(
                version_id=row[0],
                timestamp=row[1],
                action=RecordAction[row[2]],
                song=row[3]
            ))
        return logs

    def get_all_songs(self, limit=None, offset=0) -> list[SongEntry]:
        """
        获取所有歌曲，支持分页
        :param limit: 限制返回的条目数，None表示不限制
        :param offset: 偏移量
        :return: 歌曲列表，SongEntry列表
        """
        if limit is None:
            self.cursor.execute('''
            SELECT * FROM songs ORDER BY import_time DESC
            ''')
        else:
            self.cursor.execute('''
            SELECT * FROM songs ORDER BY import_time DESC LIMIT ? OFFSET ?
            ''', (limit, offset))
        rows = self.cursor.fetchall()
        songs = []
        for row in rows:
            # 兼容旧数据库结构（没有album字段的情况）
            if len(row) >= 6:  # 新结构：id, singer, song, album, hash_value, import_time
                songs.append(SongEntry(
                    id=row[0],
                    singer=row[1],
                    song=row[2],
                    album=row[3] if row[3] else '',
                    hash_value=row[4],
                    import_time=row[5]
                ))
            else:  # 旧结构：id, singer, song, hash_value, import_time
                songs.append(SongEntry(
                    id=row[0],
                    singer=row[1],
                    song=row[2],
                    album='',
                    hash_value=row[3],
                    import_time=row[4]
                ))
        return songs

    def get_songs_count(self) -> int:
        """
        获取歌曲总数
        :return: 歌曲总数
        """
        self.cursor.execute("SELECT COUNT(*) FROM songs")
        return self.cursor.fetchone()[0]

    def get_logs_count(self) -> int:
        """
        获取日志总数
        :return: 日志总数
        """
        self.cursor.execute("SELECT COUNT(*) FROM logs")
        return self.cursor.fetchone()[0]

if __name__ == "__main__":
    db = DB("music_manager.db")
    print(db.exist())


