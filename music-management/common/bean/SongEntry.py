
class SongEntry:
    def __init__(self, id, singer, song, hash_value, import_time, album=''):
        self.id = id
        self.singer = singer
        self.song = song
        self.album = album
        self.hash_value = hash_value
        self.import_time = import_time

    def __repr__(self):
        return f"SongEntry(id={self.id}, singer={self.singer}, song={self.song}, album={self.album}, hash_value={self.hash_value}, import_time={self.import_time})"