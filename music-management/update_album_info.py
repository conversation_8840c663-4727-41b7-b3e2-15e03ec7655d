#!/usr/bin/env python3
"""
更新现有数据库中的专辑信息
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from common.DB import DB
from common.MusicLibarian import MusicLibrarian
from common.CommonUtil import DEFAULT_DB_NAME, DEFAULT_MUSIC_FOLDER

def update_album_info():
    """更新数据库中所有歌曲的专辑信息"""
    print("开始更新专辑信息...")
    
    try:
        # 初始化数据库和音乐库
        db = DB(DEFAULT_DB_NAME)
        music_librarian = MusicLibrarian(DEFAULT_MUSIC_FOLDER)
        
        # 获取所有歌曲
        all_songs = db.get_all_songs()
        print(f"找到 {len(all_songs)} 首歌曲")
        
        updated_count = 0
        
        for i, song in enumerate(all_songs):
            print(f"处理第 {i+1}/{len(all_songs)} 首歌曲: {song.singer} - {song.song}")
            
            # 获取歌曲文件路径
            file_path = music_librarian.get_song_file_path(f"{song.singer} - {song.song}.mp3")
            
            if not os.path.exists(file_path):
                print(f"  文件不存在: {file_path}")
                continue
            
            # 提取专辑信息
            album = db._extract_album_info(file_path)
            
            # 如果专辑信息不同，则更新
            if album != song.album:
                try:
                    # 更新数据库
                    db.cursor.execute('''
                        UPDATE songs SET album = ? WHERE id = ?
                    ''', (album, song.id))
                    
                    print(f"  更新专辑信息: '{song.album}' -> '{album}'")
                    updated_count += 1
                    
                except Exception as e:
                    print(f"  更新失败: {e}")
            else:
                print(f"  专辑信息无需更新: '{album}'")
        
        # 提交更改
        db.conn.commit()
        print(f"\n更新完成！共更新了 {updated_count} 首歌曲的专辑信息")
        
        db.close()
        
    except Exception as e:
        print(f"更新过程中出错: {e}")

if __name__ == "__main__":
    update_album_info()
