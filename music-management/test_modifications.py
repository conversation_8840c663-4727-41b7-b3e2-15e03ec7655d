#!/usr/bin/env python3
"""
测试music-management项目的修改
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_modifications():
    """测试数据库修改"""
    print("=== 测试数据库修改 ===")
    
    try:
        from common.DB import DB
        from common.bean.SongEntry import SongEntry
        
        # 创建测试数据库
        test_db = DB("test_music_manager.db")
        
        # 测试SongEntry类是否支持album字段
        song_entry = SongEntry(
            id=1,
            singer="测试歌手",
            song="测试歌曲",
            album="测试专辑",
            hash_value="test_hash",
            import_time="2024-01-01 00:00:00"
        )
        
        print(f"SongEntry测试成功: {song_entry}")
        
        # 测试数据库表结构
        test_db.cursor.execute("PRAGMA table_info(songs)")
        columns = test_db.cursor.fetchall()
        print("数据库表结构:")
        for column in columns:
            print(f"  {column[1]} {column[2]}")
        
        # 检查是否有album字段
        column_names = [column[1] for column in columns]
        if 'album' in column_names:
            print("✓ album字段已成功添加到数据库")
        else:
            print("✗ album字段未找到")
        
        test_db.close()
        
        # 清理测试数据库
        if os.path.exists("test_music_manager.db"):
            os.remove("test_music_manager.db")
            
        print("数据库修改测试完成\n")
        
    except Exception as e:
        print(f"数据库测试失败: {e}\n")

def test_utils_modifications():
    """测试utils修改"""
    print("=== 测试utils修改 ===")
    
    try:
        from ui.utils import get_default_cover, get_song_album
        
        # 测试默认封面函数
        default_cover = get_default_cover()
        if default_cover is not None:
            print("✓ get_default_cover函数工作正常")
        else:
            print("✗ get_default_cover函数返回None")
        
        # 测试专辑信息提取函数
        # 使用一个不存在的文件路径进行测试
        album = get_song_album("non_existent_file.mp3")
        print(f"✓ get_song_album函数工作正常，返回: '{album}'")
        
        print("utils修改测试完成\n")
        
    except Exception as e:
        print(f"utils测试失败: {e}\n")

def test_imports():
    """测试所有导入是否正常"""
    print("=== 测试导入 ===")
    
    try:
        # 测试核心模块导入
        from common.DB import DB
        from common.bean.SongEntry import SongEntry
        from common.MusicLibarian import MusicLibrarian
        from MusicManager import MusicManager
        print("✓ 核心模块导入成功")
        
        # 测试UI模块导入（可能会因为PyQt5缺失而失败）
        try:
            from ui.utils import get_default_cover, get_song_album
            print("✓ UI utils模块导入成功")
        except ImportError as e:
            print(f"⚠ UI模块导入失败（可能缺少PyQt5）: {e}")
        
        print("导入测试完成\n")
        
    except Exception as e:
        print(f"导入测试失败: {e}\n")

def main():
    """主测试函数"""
    print("开始测试music-management项目修改...")
    print("=" * 50)
    
    test_imports()
    test_database_modifications()
    test_utils_modifications()
    
    print("=" * 50)
    print("测试完成！")

if __name__ == "__main__":
    main()
