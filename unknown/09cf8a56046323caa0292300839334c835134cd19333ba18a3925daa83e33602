from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QHeaderView)


class LogTableWidget(QWidget):
    """日志表格组件"""
    
    def __init__(self, music_manager, config):
        super().__init__()
        self.music_manager = music_manager
        self.config = config
        self.current_page = 0
        self.total_pages = 0
        self.logs_per_page = config.get('entry_per_page', 100)
        
        self.init_ui()
        self.load_logs()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(['版本号', '时间', '操作', '歌曲'])
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.verticalHeader().setVisible(False)
        
        # 设置列宽 - 支持手动调整
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 版本号列可调整
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # 时间列可调整
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # 操作列可调整
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # 歌曲列可调整

        # 设置初始列宽，让表格占满整个宽度
        self.set_initial_column_widths()

        layout.addWidget(self.table)
        
        # 分页控件
        page_layout = QHBoxLayout()
        page_layout.setContentsMargins(0, 2, 0, 2)  # 进一步减少边距

        self.prev_button = QPushButton('上一页')
        self.prev_button.clicked.connect(self.prev_page)
        self.prev_button.setMaximumWidth(80)  # 限制按钮宽度
        page_layout.addWidget(self.prev_button)

        self.page_label = QLabel('第 1 页，共 1 页')
        self.page_label.setAlignment(Qt.AlignCenter)
        page_layout.addWidget(self.page_label)

        self.next_button = QPushButton('下一页')
        self.next_button.clicked.connect(self.next_page)
        self.next_button.setMaximumWidth(80)  # 限制按钮宽度
        page_layout.addWidget(self.next_button)

        layout.addLayout(page_layout)
        self.setLayout(layout)

        # 设置定时器用于监听窗口大小变化
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self.adjust_column_widths)

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        # 延迟调整列宽，避免频繁调整
        self.resize_timer.stop()
        self.resize_timer.start(100)

    def set_initial_column_widths(self):
        """设置初始列宽，让表格占满整个宽度"""
        # 使用QTimer延迟执行，确保表格已经完全渲染
        QTimer.singleShot(100, self._set_column_widths_delayed)

    def adjust_column_widths(self):
        """调整列宽以适应当前窗口大小"""
        self._set_column_widths_delayed()

    def _set_column_widths_delayed(self):
        """延迟设置列宽"""
        # 获取表格实际宽度
        table_width = self.table.width()
        if table_width < 100:  # 如果还没有正确的宽度，再次延迟
            QTimer.singleShot(100, self._set_column_widths_delayed)
            return

        # 更精确地计算可用宽度，减去表格边框和内边距
        frame_width = self.table.frameWidth() * 2  # 表格边框宽度
        # 检查是否有垂直滚动条
        scrollbar_width = 0
        if self.table.verticalScrollBar().isVisible():
            scrollbar_width = self.table.verticalScrollBar().sizeHint().width()

        # 计算总可用宽度
        total_width = table_width - frame_width - scrollbar_width - 2

        # 确保总宽度为正数
        if total_width < 100:
            total_width = 100

        # 按比例分配宽度
        version_width = int(total_width * 0.15)  # 15%
        time_width = int(total_width * 0.25)     # 25%
        action_width = int(total_width * 0.15)   # 15%
        song_width = total_width - version_width - time_width - action_width  # 剩余45%

        self.table.setColumnWidth(0, version_width)
        self.table.setColumnWidth(1, time_width)
        self.table.setColumnWidth(2, action_width)
        self.table.setColumnWidth(3, song_width)

    def load_logs(self):
        """加载日志数据"""
        try:
            # 获取总数
            total_count = self.music_manager.DB.get_logs_count()
            self.total_pages = (total_count + self.logs_per_page - 1) // self.logs_per_page
            
            # 获取当前页数据
            offset = self.current_page * self.logs_per_page
            logs = self.music_manager.DB.get_all_logs(self.logs_per_page, offset)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充表格
            for i, log in enumerate(logs):
                self.table.insertRow(i)
                
                # 版本号列
                version_item = QTableWidgetItem(str(log.version_id))
                version_item.setFlags(version_item.flags() & ~Qt.ItemIsEditable)
                version_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 0, version_item)

                # 时间列
                time_item = QTableWidgetItem(log.timestamp)
                time_item.setFlags(time_item.flags() & ~Qt.ItemIsEditable)
                time_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 1, time_item)

                # 操作列
                action_text = self.get_action_text(log.action)
                action_item = QTableWidgetItem(action_text)
                action_item.setFlags(action_item.flags() & ~Qt.ItemIsEditable)
                action_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 2, action_item)

                # 歌曲列
                song_item = QTableWidgetItem(log.song)
                song_item.setFlags(song_item.flags() & ~Qt.ItemIsEditable)
                song_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 3, song_item)
            
            self.update_page_controls()
            
        except Exception as e:
            print(f"加载日志数据时出错: {e}")
    
    def get_action_text(self, action):
        """获取操作文本"""
        action_map = {
            'ADD_SONG': '添加歌曲',
            'DELETE_SONG': '删除歌曲'
        }
        if hasattr(action, 'name'):
            return action_map.get(action.name, action.name)
        else:
            return action_map.get(str(action), str(action))
    
    def update_page_controls(self):
        """更新分页控件状态"""
        self.page_label.setText(f'第 {self.current_page + 1} 页，共 {max(1, self.total_pages)} 页')
        self.prev_button.setEnabled(self.current_page > 0)
        self.next_button.setEnabled(self.current_page < self.total_pages - 1)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_logs()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.load_logs()
    
    def refresh(self):
        """刷新数据"""
        self.load_logs()
    
    def update_config(self, config):
        """更新配置"""
        self.config = config
        old_per_page = self.logs_per_page
        self.logs_per_page = config.get('entry_per_page', 100)
        
        # 如果每页条目数改变，重新计算当前页
        if old_per_page != self.logs_per_page:
            current_item = self.current_page * old_per_page
            self.current_page = current_item // self.logs_per_page
            self.load_logs()
